import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';

void main() {
  group('ConfirmationButtons Widget Tests', () {
    late bool onTapCalled;
    late bool onCancelCalled;

    setUp(() {
      onTapCalled = false;
      onCancelCalled = false;
    });

    Widget createTestWidget({
      required bool isValid,
      String btnName = 'Save',
      bool showCancelButton = false,
      String cancelButtonName = 'Cancel',
      bool preventMultipleClicks = true,
      Duration debounceDelay = const Duration(milliseconds: 100), // Shorter for tests
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ConfirmationButtons(
            btnName: btnName,
            isValid: isValid,
            showCancelButton: showCancelButton,
            cancelButtonName: cancelButtonName,
            preventMultipleClicks: preventMultipleClicks,
            debounceDelay: debounceDelay,
            onTap: () {
              onTapCalled = true;
            },
            onCancel: () {
              onCancelCalled = true;
            },
          ),
        ),
      );
    }

    testWidgets('should enable save button when form is valid', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: true));

      final saveButton = find.byType(ElevatedButton);
      expect(saveButton, findsOneWidget);

      final elevatedButton = tester.widget<ElevatedButton>(saveButton);
      expect(elevatedButton.onPressed, isNotNull, reason: 'Save button should be enabled when form is valid');
    });

    testWidgets('should disable save button when form is invalid', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: false));

      final saveButton = find.byType(ElevatedButton);
      expect(saveButton, findsOneWidget);

      final elevatedButton = tester.widget<ElevatedButton>(saveButton);
      expect(elevatedButton.onPressed, isNull, reason: 'Save button should be disabled when form is invalid');
    });

    testWidgets('should call onTap when valid button is pressed', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: true));

      final saveButton = find.byType(ElevatedButton);
      await tester.tap(saveButton);
      await tester.pump();

      expect(onTapCalled, isTrue, reason: 'onTap should be called when valid button is pressed');
    });

    testWidgets('should not call onTap when invalid button is pressed', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: false));

      final saveButton = find.byType(ElevatedButton);
      await tester.tap(saveButton);
      await tester.pump();

      expect(onTapCalled, isFalse, reason: 'onTap should not be called when invalid button is pressed');
    });

    testWidgets('should show loading indicator during processing', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: true));

      // Tap the button to start processing
      final saveButton = find.byType(ElevatedButton);
      await tester.tap(saveButton);
      await tester.pump();

      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should disable button during processing', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: true));

      // Tap the button to start processing
      final saveButton = find.byType(ElevatedButton);
      await tester.tap(saveButton);
      await tester.pump();

      // Button should be disabled during processing
      final elevatedButton = tester.widget<ElevatedButton>(saveButton);
      expect(elevatedButton.onPressed, isNull, reason: 'Button should be disabled during processing');
    });

    testWidgets('should re-enable button after processing completes', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: true));

      // Tap the button to start processing
      final saveButton = find.byType(ElevatedButton);
      await tester.tap(saveButton);
      await tester.pump();

      // Wait for debounce delay to complete
      await tester.pump(const Duration(milliseconds: 150));

      // Button should be re-enabled after processing
      final elevatedButton = tester.widget<ElevatedButton>(saveButton);
      expect(elevatedButton.onPressed, isNotNull, reason: 'Button should be re-enabled after processing completes');
    });

    testWidgets('should show cancel button when showCancelButton is true', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: true, showCancelButton: true));

      expect(find.byType(TextButton), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets('should call onCancel when cancel button is pressed', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: true, showCancelButton: true));

      final cancelButton = find.byType(TextButton);
      await tester.tap(cancelButton);
      await tester.pump();

      expect(onCancelCalled, isTrue, reason: 'onCancel should be called when cancel button is pressed');
    });

    testWidgets('should prevent multiple rapid clicks when preventMultipleClicks is true', (WidgetTester tester) async {
      int tapCount = 0;

      final widget = MaterialApp(
        home: Scaffold(
          body: ConfirmationButtons(
            btnName: 'Save',
            isValid: true,
            preventMultipleClicks: true,
            debounceDelay: const Duration(milliseconds: 100),
            onTap: () {
              tapCount++;
            },
          ),
        ),
      );

      await tester.pumpWidget(widget);

      final saveButton = find.byType(ElevatedButton);

      // Tap multiple times rapidly
      await tester.tap(saveButton);
      await tester.tap(saveButton);
      await tester.tap(saveButton);
      await tester.pump();

      expect(tapCount, equals(1), reason: 'Only one tap should be processed when preventMultipleClicks is true');
    });

    testWidgets('should display correct button text', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: true, btnName: 'Submit'));

      expect(find.text('Submit'), findsOneWidget);
    });

    testWidgets('should display correct cancel button text', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isValid: true, showCancelButton: true, cancelButtonName: 'Back'));

      expect(find.text('Back'), findsOneWidget);
    });
  });
}
