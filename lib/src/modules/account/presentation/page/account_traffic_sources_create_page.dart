import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/traffic_sources_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/traffic_sources_state.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_cubit.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/modules/survey/presentation/widget/social_info_tab_view.dart';

class AccountTrafficSourcesCreatePage extends StatefulWidget {
  const AccountTrafficSourcesCreatePage({super.key});

  @override
  State<AccountTrafficSourcesCreatePage> createState() => _AccountTrafficSourcesCreatePageState();
}

class _AccountTrafficSourcesCreatePageState extends State<AccountTrafficSourcesCreatePage> {
  late AccountCubit cubit = Modular.get<AccountCubit>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: Container(
        padding: EdgeInsets.symmetric(vertical: 12.r, horizontal: 16.r),
        color: Colors.white,
        child: Column(
          spacing: 16.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [_buildHeader(), _buildBody()],
        ),
      ),
      bottomSheet: _buildFooter(),
    );
  }

  void emitSocialInfo(SocialInfo socialInfo) {
    ReadContext(context).read<TrafficSourcesCubit>().emitSocialInfo(socialInfo);
  }

  Widget _buildFooter() {
    return BlocBuilder<TrafficSourcesCubit, TrafficSourcesState>(builder: (_, state) {
      return Container(
        color: Colors.white,
        padding: EdgeInsets.all(16.r),
        child: ConfirmationButtons(
            btnName: 'Save',
            isValid: state.isFormValid,
            showCancelButton: true,
            onTap: () async {
              if (mounted) {
                cubit.showLoading();
                final trafficSourcesCubit = ReadContext(context).read<TrafficSourcesCubit>();
                final result = await trafficSourcesCubit.upsertSocialInfo(state.socialInfo);

                if (result) {
                  List<SocialInfo> trafficSources = List.from(cubit.state.trafficSources);
                  if (state.socialInfo.id == null) {
                    trafficSources.insert(0, trafficSourcesCubit.state.socialInfo);
                  }
                  await cubit.updateAccount(cubit.state.copyWith(trafficSources: trafficSources));
                  emitSocialInfo(trafficSourcesCubit.state.socialInfo);
                  cubit.hideLoading();
                  Modular.to.pop(state.socialInfo.name);
                } else {
                  cubit.hideLoading();
                  final errorMessage = trafficSourcesCubit.state.errorMessage;
                  if (errorMessage != null && errorMessage.isNotEmpty && mounted) {
                    HapticFeedback.lightImpact();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(errorMessage),
                        backgroundColor: Colors.red,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                }
              }
            }),
      );
    });
  }

  Widget _buildBody() {
    return BlocBuilder<TrafficSourcesCubit, TrafficSourcesState>(builder: (_, state) {
      return SocialInfoTabView(
          socialInfo: state.socialInfo,
          onEmitSocialInfo: emitSocialInfo,
          onFollowerNumber: (item) {
            ReadContext(context)
                .read<TrafficSourcesCubit>()
                .emitSocialInfo(state.socialInfo.copyWith(totalFollowerLevel: item));
          },
          onClearForm: () {});
    });
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        BlocBuilder<SurveyTabCubit, SurveyTabState>(builder: (context, state) {
          return Text(
            state.currentTab == 0 ? 'Add social media channel' : 'Add Website',
            style: Theme.of(context).textTheme.titleSmall,
          );
        }),
        GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: CircleAvatar(
            backgroundColor: Colors.grey[200],
            radius: 12.r,
            child: Icon(Icons.close, size: 16.r),
          ),
        ),
      ],
    );
  }
}
