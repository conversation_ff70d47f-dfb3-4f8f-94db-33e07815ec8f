import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/traffic_sources_cubit.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_cubit.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/modules/survey/presentation/widget/social_info_tab_view.dart';
import 'package:koc_app/src/shared/validator/validators.dart';

class AccountTrafficSourcesCreatePage extends StatefulWidget {
  const AccountTrafficSourcesCreatePage({super.key});

  @override
  State<AccountTrafficSourcesCreatePage> createState() => _AccountTrafficSourcesCreatePageState();
}

class _AccountTrafficSourcesCreatePageState extends State<AccountTrafficSourcesCreatePage> {
  late AccountCubit cubit = Modular.get<AccountCubit>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: Container(
        padding: EdgeInsets.symmetric(vertical: 12.r, horizontal: 16.r),
        color: Colors.white,
        child: Column(
          spacing: 16.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [_buildHeader(), _buildBody()],
        ),
      ),
      bottomSheet: _buildFooter(),
    );
  }

  void emitSocialInfo(SocialInfo socialInfo) {
    ReadContext(context).read<TrafficSourcesCubit>().emitSocialInfo(socialInfo);
  }

  Widget _buildFooter() {
    return BlocBuilder<TrafficSourcesCubit, SocialInfo>(builder: (_, socialInfo) {
      return Container(
        color: Colors.white,
        padding: EdgeInsets.all(16.r),
        child: ConfirmationButtons(
            btnName: 'Save',
            isValid: isEnableSaveButton(ReadContext(context).read<TrafficSourcesCubit>(), socialInfo),
            showCancelButton: true,
            onTap: () async {
              if (mounted) {
                cubit.showLoading();
                final trafficSourcesCubit = ReadContext(context).read<TrafficSourcesCubit>();
                final result = await trafficSourcesCubit.upsertSocialInfo(socialInfo);

                if (result) {
                  List<SocialInfo> trafficSources = List.from(cubit.state.trafficSources);
                  if (socialInfo.id == null) {
                    trafficSources.insert(0, trafficSourcesCubit.state);
                  }
                  await cubit.updateAccount(cubit.state.copyWith(trafficSources: trafficSources));
                  emitSocialInfo(trafficSourcesCubit.state);
                  cubit.hideLoading();
                  Modular.to.pop(socialInfo.name);
                } else {
                  cubit.hideLoading();
                  final errorMessage = trafficSourcesCubit.state.errorMessage;
                  if (errorMessage != null && errorMessage.isNotEmpty && mounted) {
                    HapticFeedback.lightImpact();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(errorMessage),
                        backgroundColor: Colors.red,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                }
              }
            }),
      );
    });
  }

  bool isEnableSaveButton(TrafficSourcesCubit trafficSourcesCubit, SocialInfo socialInfo) {
    if (trafficSourcesCubit.state.isSubmitting) return false;

    final isNameValid = socialInfo.name.isNotEmpty;
    final isUrlValid = socialInfo.url.isNotEmpty && Validators.isValidUrl(socialInfo.url);

    if (!isNameValid || !isUrlValid) return false;

    // Use centralized duplicate validation from cubit
    if (trafficSourcesCubit.isDuplicateSiteName(socialInfo.name, socialInfo.id)) return false;

    if (Modular.get<SurveyTabCubit>().state.currentTab == 1) {
      return true;
    }

    return Validators.isValidSocialUrl(socialInfo.url);
  }

  Widget _buildBody() {
    return BlocBuilder<TrafficSourcesCubit, SocialInfo>(builder: (_, socialInfo) {
      return SocialInfoTabView(
          socialInfo: socialInfo,
          onEmitSocialInfo: emitSocialInfo,
          onFollowerNumber: (item) {
            ReadContext(context)
                .read<TrafficSourcesCubit>()
                .emitSocialInfo(socialInfo.copyWith(totalFollowerLevel: item));
          },
          onClearForm: () {});
    });
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        BlocBuilder<SurveyTabCubit, SurveyTabState>(builder: (context, state) {
          return Text(
            state.currentTab == 0 ? 'Add social media channel' : 'Add Website',
            style: Theme.of(context).textTheme.titleSmall,
          );
        }),
        GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: CircleAvatar(
            backgroundColor: Colors.grey[200],
            radius: 12.r,
            child: Icon(Icons.close, size: 16.r),
          ),
        ),
      ],
    );
  }
}
