import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/cubit/common/common_cubit.dart';
import 'package:koc_app/src/base/cubit/common/common_state.dart';

class ConfirmationButtons extends StatefulWidget {
  final String btnName;
  final void Function() onTap;
  final bool isValid;
  final bool showCancelButton;
  final void Function()? onCancel;
  final MainAxisAlignment? alignment;
  final String cancelButtonName;
  final bool preventMultipleClicks;
  final Duration debounceDelay;

  const ConfirmationButtons(
      {super.key,
      required this.btnName,
      required this.onTap,
      this.isValid = false,
      this.showCancelButton = false,
      this.onCancel,
      this.alignment,
      this.cancelButtonName = 'Cancel',
      this.preventMultipleClicks = true,
      this.debounceDelay = const Duration(milliseconds: 500)});

  @override
  State<ConfirmationButtons> createState() => _ConfirmationButtonsState();
}

class _ConfirmationButtonsState extends State<ConfirmationButtons> {
  bool _isProcessing = false;
  DateTime? _lastClickTime;

  bool _canClick() {
    if (!widget.isValid || _isProcessing) return false;

    if (!widget.preventMultipleClicks) return true;

    final now = DateTime.now();
    if (_lastClickTime != null && now.difference(_lastClickTime!) < widget.debounceDelay) {
      return false;
    }

    return true;
  }

  void _handleTap() async {
    if (!_canClick()) return;

    setState(() {
      _isProcessing = true;
      _lastClickTime = DateTime.now();
    });

    try {
      widget.onTap();
    } finally {
      // Reset processing state after a short delay to prevent immediate re-clicks
      Future.delayed(widget.debounceDelay, () {
        if (mounted) {
          setState(() {
            _isProcessing = false;
          });
        }
      });
    }
  }

  /// Safely gets the CommonCubit state if available, returns null if not found
  CommonState? _getCommonState() {
    try {
      return Modular.get<CommonCubit>().state;
    } catch (e) {
      // CommonCubit not available in Modular, return null
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Try to get CommonState, but don't fail if CommonCubit is not available
    final commonState = _getCommonState();
    final isGlobalLoading = commonState?.isLoading ?? false;

    final isButtonDisabled = !widget.isValid || _isProcessing || isGlobalLoading;

    return Row(
      spacing: 8.r,
      mainAxisAlignment:
          widget.alignment ?? (widget.showCancelButton ? MainAxisAlignment.spaceBetween : MainAxisAlignment.end),
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (widget.showCancelButton)
          TextButton(
              onPressed: _isProcessing || isGlobalLoading
                  ? null
                  : () {
                      if (widget.onCancel != null) {
                        widget.onCancel!();
                      } else {
                        Modular.to.pop();
                      }
                    },
              child: Text(widget.cancelButtonName,
                  style: Theme.of(context).textTheme.labelLarge!.copyWith(
                      color: _isProcessing || isGlobalLoading ? Colors.grey : const Color(0xFFEF6507),
                      fontWeight: FontWeight.w500))),
        ElevatedButton(
            onPressed: isButtonDisabled ? null : _handleTap,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(widget.btnName,
                    style: Theme.of(context)
                        .textTheme
                        .labelLarge!
                        .copyWith(color: Colors.white, fontWeight: FontWeight.w500)),
                if (_isProcessing || isGlobalLoading)
                  Padding(
                    padding: EdgeInsets.only(left: 8.r),
                    child: SizedBox(
                      width: 16.r,
                      height: 16.r,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                  ),
              ],
            ))
      ],
    );
  }
}
