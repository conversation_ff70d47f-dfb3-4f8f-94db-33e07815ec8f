import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/cubit/common/common_cubit.dart';
import 'package:koc_app/src/base/cubit/common/common_state.dart';
import 'package:koc_app/src/base/mixin/debounce_mixin.dart';

class ConfirmationButtons extends StatefulWidget {
  final String btnName;
  final void Function() onTap;
  final bool isValid;
  final bool showCancelButton;
  final void Function()? onCancel;
  final MainAxisAlignment? alignment;
  final String cancelButtonName;
  final bool preventMultipleClicks;
  final Duration debounceDelay;

  const ConfirmationButtons(
      {super.key,
      required this.btnName,
      required this.onTap,
      this.isValid = false,
      this.showCancelButton = false,
      this.onCancel,
      this.alignment,
      this.cancelButtonName = 'Cancel',
      this.preventMultipleClicks = true,
      this.debounceDelay = const Duration(milliseconds: 500)});

  @override
  State<ConfirmationButtons> createState() => _ConfirmationButtonsState();
}

class _ConfirmationButtonsState extends State<ConfirmationButtons> with DebounceMixin {
  @override
  Duration get debounceDelay => widget.debounceDelay;

  @override
  bool get preventMultipleClicks => widget.preventMultipleClicks;

  bool _canClick() {
    return widget.isValid && canProcessAction();
  }

  void _handleTap() async {
    if (!_canClick()) return;

    executeWithDebounce(() {
      widget.onTap();
    });
  }

  /// Safely gets the CommonCubit state if available, returns null if not found
  CommonState? _getCommonState() {
    final commonCubit = Modular.tryGet<CommonCubit>();
    return commonCubit?.state;
  }

  @override
  Widget build(BuildContext context) {
    // Try to get CommonState, but don't fail if CommonCubit is not available
    final commonState = _getCommonState();
    final isGlobalLoading = commonState?.isLoading ?? false;

    final isButtonDisabled = !widget.isValid || isProcessing || isGlobalLoading;

    return Row(
      spacing: 8.r,
      mainAxisAlignment:
          widget.alignment ?? (widget.showCancelButton ? MainAxisAlignment.spaceBetween : MainAxisAlignment.end),
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (widget.showCancelButton)
          TextButton(
              onPressed: isProcessing || isGlobalLoading
                  ? null
                  : () {
                      if (widget.onCancel != null) {
                        widget.onCancel!();
                      } else {
                        Modular.to.pop();
                      }
                    },
              child: Text(widget.cancelButtonName,
                  style: Theme.of(context).textTheme.labelLarge!.copyWith(
                      color: isProcessing || isGlobalLoading ? Colors.grey : const Color(0xFFEF6507),
                      fontWeight: FontWeight.w500))),
        ElevatedButton(
            onPressed: isButtonDisabled ? null : _handleTap,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(widget.btnName,
                    style: Theme.of(context)
                        .textTheme
                        .labelLarge!
                        .copyWith(color: Colors.white, fontWeight: FontWeight.w500)),
                if (isProcessing || isGlobalLoading)
                  Padding(
                    padding: EdgeInsets.only(left: 8.r),
                    child: SizedBox(
                      width: 16.r,
                      height: 16.r,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        semanticsLabel: 'Processing request',
                      ),
                    ),
                  ),
              ],
            ))
      ],
    );
  }
}
