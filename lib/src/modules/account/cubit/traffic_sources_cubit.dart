import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/traffic_sources_state.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/services/cache_invalidation_service.dart';
import 'package:koc_app/src/shared/services/url_helpers.dart';
import 'package:koc_app/src/shared/validator/validators.dart';
import '../../../shared/utils/handle_error.dart';

class TrafficSourcesCubit extends BaseCubit<TrafficSourcesState> {
  final AccountRepository _accountRepository;
  TrafficSourcesCubit(this._accountRepository) : super(const TrafficSourcesState());

  void emitSocialInfo(SocialInfo socialInfo) {
    // Perform real-time validation
    final isNameValid = socialInfo.name.isNotEmpty;
    final isUrlValid = socialInfo.url.isNotEmpty && Validators.isValidUrl(socialInfo.url);
    final hasDuplicateName = isDuplicateSiteName(socialInfo.name, socialInfo.id);

    emit(state.copyWith(
      socialInfo: socialInfo,
      isNameValid: isNameValid,
      isUrlValid: isUrlValid,
      hasDuplicateName: hasDuplicateName,
    ));
  }

  void updateFollowerNumber(FollowerNumber followerNumber) {
    final updatedSocialInfo = state.socialInfo.copyWith(totalFollowerLevel: followerNumber);
    emitSocialInfo(updatedSocialInfo);
  }

  void updateId(int id) {
    final updatedSocialInfo = state.socialInfo.copyWith(id: id);
    emitSocialInfo(updatedSocialInfo);
  }

  void updateSocialType(SocialType socialMediaType) {
    final updatedSocialInfo = state.socialInfo.copyWith(socialMediaType: socialMediaType);
    emitSocialInfo(updatedSocialInfo);
  }

  /// Updates validation state in real-time as user types
  void validateForm(SocialInfo socialInfo) {
    emit(state.copyWith(
      socialInfo: socialInfo,
      isNameValid: socialInfo.name.isNotEmpty,
      isUrlValid: socialInfo.url.isNotEmpty && Validators.isValidUrl(socialInfo.url),
      hasDuplicateName: isDuplicateSiteName(socialInfo.name, socialInfo.id),
    ));
  }

  /// Validates if a site name is unique among existing traffic sources
  /// Returns true if the name is a duplicate, false if it's unique
  bool isDuplicateSiteName(String name, int? excludeId) {
    final accountCubit = Modular.get<AccountCubit>();
    final existingSites = accountCubit.state.trafficSources;

    return existingSites
        .any((site) => site.name.toLowerCase().trim() == name.toLowerCase().trim() && site.id != excludeId);
  }

  Future<bool> upsertSocialInfo(SocialInfo socialInfo) async {
    if (state.isSubmitting) {
      return false;
    }

    try {
      emit(state.copyWith(isSubmitting: true, errorMessage: null));

      if (isDuplicateSiteName(socialInfo.name, socialInfo.id)) {
        emit(state.copyWith(
          errorMessage: 'A site with this name already exists. Please choose a different name.',
          isSubmitting: false,
        ));
        return false;
      }

      final type = getSocialTypeFromUrl(socialInfo.url);
      var updatedSource = socialInfo.copyWith(socialMediaType: type);
      if (type == SocialType.OTHER) {
        updatedSource = updatedSource.copyWith(
          totalFollowerLevel: FollowerNumber.EMPTY,
        );
      }
      final siteId = await _accountRepository.updateTrafficSources(updatedSource);

      await CacheInvalidationService().invalidateCacheAfterSiteCreation(siteId);

      final siteCubit = Modular.get<SiteCubit>();
      siteCubit.markSiteCreated();

      // Update the social info with the returned ID and type
      final finalSocialInfo = updatedSource.copyWith(id: siteId);

      emit(state.copyWith(
        socialInfo: finalSocialInfo,
        errorMessage: null,
        isSubmitting: false,
      ));
      return true;
    } catch (e) {
      emit(state.copyWith(isSubmitting: false));
      handleError(e, (message) => emit(state.copyWith(errorMessage: message, isSubmitting: false)));
    }
    return false;
  }

  Future<bool> deleteSocialInfo(int id) async {
    try {
      await _accountRepository.deleteTrafficSources(id);

      await CacheInvalidationService().invalidateSiteCache(specificSiteId: id);

      final sites = await commonCubit.sharedPreferencesService.getSites();
      final updatedSites = sites.where((site) => site.id != id).toList();
      await commonCubit.sharedPreferencesService.setSites(updatedSites);
      final siteCubit = Modular.get<SiteCubit>();
      await siteCubit.reloadSites();
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }
}
