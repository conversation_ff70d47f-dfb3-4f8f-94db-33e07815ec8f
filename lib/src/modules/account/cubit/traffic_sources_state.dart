import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/validator/validators.dart';

part 'traffic_sources_state.freezed.dart';
part 'traffic_sources_state.g.dart';

@freezed
class TrafficSourcesState extends BaseCubitState with _$TrafficSourcesState {
  const factory TrafficSourcesState({
    @Default(SocialInfo()) SocialInfo socialInfo,
    @Default(false) bool hasDuplicateName,
    @Default(false) bool isNameValid,
    @Default(false) bool isUrlValid,
    @Default(false) bool isSubmitting,
    String? errorMessage,
  }) = _TrafficSourcesState;

  factory TrafficSourcesState.fromJson(Map<String, Object?> json) => _$TrafficSourcesStateFromJson(json);
}

extension TrafficSourcesStateExtension on TrafficSourcesState {
  /// Computed property to check if the save button should be enabled
  bool get isFormValid {
    if (!isNameValid || !isUrlValid || hasDuplicateName || isSubmitting) {
      return false;
    }

    // For social media (non-OTHER types), validate social URL format
    if (socialInfo.socialMediaType != SocialType.OTHER) {
      return Validators.isValidSocialUrl(socialInfo.url);
    }

    return true;
  }
}
