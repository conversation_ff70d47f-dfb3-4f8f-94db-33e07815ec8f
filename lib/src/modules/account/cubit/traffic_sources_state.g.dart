// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'traffic_sources_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TrafficSourcesStateImpl _$$TrafficSourcesStateImplFromJson(
        Map<String, dynamic> json) =>
    _$TrafficSourcesStateImpl(
      socialInfo: json['socialInfo'] == null
          ? const SocialInfo()
          : SocialInfo.fromJson(json['socialInfo'] as Map<String, dynamic>),
      hasDuplicateName: json['hasDuplicateName'] as bool? ?? false,
      isNameValid: json['isNameValid'] as bool? ?? false,
      isUrlValid: json['isUrlValid'] as bool? ?? false,
      isSubmitting: json['isSubmitting'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$$TrafficSourcesStateImplToJson(
        _$TrafficSourcesStateImpl instance) =>
    <String, dynamic>{
      'socialInfo': instance.socialInfo,
      'hasDuplicateName': instance.hasDuplicateName,
      'isNameValid': instance.isNameValid,
      'isUrlValid': instance.isUrlValid,
      'isSubmitting': instance.isSubmitting,
      'errorMessage': instance.errorMessage,
    };
