// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'traffic_sources_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TrafficSourcesState _$TrafficSourcesStateFromJson(Map<String, dynamic> json) {
  return _TrafficSourcesState.fromJson(json);
}

/// @nodoc
mixin _$TrafficSourcesState {
  SocialInfo get socialInfo => throw _privateConstructorUsedError;
  bool get hasDuplicateName => throw _privateConstructorUsedError;
  bool get isNameValid => throw _privateConstructorUsedError;
  bool get isUrlValid => throw _privateConstructorUsedError;
  bool get isSubmitting => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this TrafficSourcesState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TrafficSourcesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TrafficSourcesStateCopyWith<TrafficSourcesState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TrafficSourcesStateCopyWith<$Res> {
  factory $TrafficSourcesStateCopyWith(
          TrafficSourcesState value, $Res Function(TrafficSourcesState) then) =
      _$TrafficSourcesStateCopyWithImpl<$Res, TrafficSourcesState>;
  @useResult
  $Res call(
      {SocialInfo socialInfo,
      bool hasDuplicateName,
      bool isNameValid,
      bool isUrlValid,
      bool isSubmitting,
      String? errorMessage});

  $SocialInfoCopyWith<$Res> get socialInfo;
}

/// @nodoc
class _$TrafficSourcesStateCopyWithImpl<$Res, $Val extends TrafficSourcesState>
    implements $TrafficSourcesStateCopyWith<$Res> {
  _$TrafficSourcesStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TrafficSourcesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? socialInfo = null,
    Object? hasDuplicateName = null,
    Object? isNameValid = null,
    Object? isUrlValid = null,
    Object? isSubmitting = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      socialInfo: null == socialInfo
          ? _value.socialInfo
          : socialInfo // ignore: cast_nullable_to_non_nullable
              as SocialInfo,
      hasDuplicateName: null == hasDuplicateName
          ? _value.hasDuplicateName
          : hasDuplicateName // ignore: cast_nullable_to_non_nullable
              as bool,
      isNameValid: null == isNameValid
          ? _value.isNameValid
          : isNameValid // ignore: cast_nullable_to_non_nullable
              as bool,
      isUrlValid: null == isUrlValid
          ? _value.isUrlValid
          : isUrlValid // ignore: cast_nullable_to_non_nullable
              as bool,
      isSubmitting: null == isSubmitting
          ? _value.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of TrafficSourcesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SocialInfoCopyWith<$Res> get socialInfo {
    return $SocialInfoCopyWith<$Res>(_value.socialInfo, (value) {
      return _then(_value.copyWith(socialInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TrafficSourcesStateImplCopyWith<$Res>
    implements $TrafficSourcesStateCopyWith<$Res> {
  factory _$$TrafficSourcesStateImplCopyWith(_$TrafficSourcesStateImpl value,
          $Res Function(_$TrafficSourcesStateImpl) then) =
      __$$TrafficSourcesStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SocialInfo socialInfo,
      bool hasDuplicateName,
      bool isNameValid,
      bool isUrlValid,
      bool isSubmitting,
      String? errorMessage});

  @override
  $SocialInfoCopyWith<$Res> get socialInfo;
}

/// @nodoc
class __$$TrafficSourcesStateImplCopyWithImpl<$Res>
    extends _$TrafficSourcesStateCopyWithImpl<$Res, _$TrafficSourcesStateImpl>
    implements _$$TrafficSourcesStateImplCopyWith<$Res> {
  __$$TrafficSourcesStateImplCopyWithImpl(_$TrafficSourcesStateImpl _value,
      $Res Function(_$TrafficSourcesStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of TrafficSourcesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? socialInfo = null,
    Object? hasDuplicateName = null,
    Object? isNameValid = null,
    Object? isUrlValid = null,
    Object? isSubmitting = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$TrafficSourcesStateImpl(
      socialInfo: null == socialInfo
          ? _value.socialInfo
          : socialInfo // ignore: cast_nullable_to_non_nullable
              as SocialInfo,
      hasDuplicateName: null == hasDuplicateName
          ? _value.hasDuplicateName
          : hasDuplicateName // ignore: cast_nullable_to_non_nullable
              as bool,
      isNameValid: null == isNameValid
          ? _value.isNameValid
          : isNameValid // ignore: cast_nullable_to_non_nullable
              as bool,
      isUrlValid: null == isUrlValid
          ? _value.isUrlValid
          : isUrlValid // ignore: cast_nullable_to_non_nullable
              as bool,
      isSubmitting: null == isSubmitting
          ? _value.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TrafficSourcesStateImpl implements _TrafficSourcesState {
  const _$TrafficSourcesStateImpl(
      {this.socialInfo = const SocialInfo(),
      this.hasDuplicateName = false,
      this.isNameValid = false,
      this.isUrlValid = false,
      this.isSubmitting = false,
      this.errorMessage});

  factory _$TrafficSourcesStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$TrafficSourcesStateImplFromJson(json);

  @override
  @JsonKey()
  final SocialInfo socialInfo;
  @override
  @JsonKey()
  final bool hasDuplicateName;
  @override
  @JsonKey()
  final bool isNameValid;
  @override
  @JsonKey()
  final bool isUrlValid;
  @override
  @JsonKey()
  final bool isSubmitting;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'TrafficSourcesState(socialInfo: $socialInfo, hasDuplicateName: $hasDuplicateName, isNameValid: $isNameValid, isUrlValid: $isUrlValid, isSubmitting: $isSubmitting, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TrafficSourcesStateImpl &&
            (identical(other.socialInfo, socialInfo) ||
                other.socialInfo == socialInfo) &&
            (identical(other.hasDuplicateName, hasDuplicateName) ||
                other.hasDuplicateName == hasDuplicateName) &&
            (identical(other.isNameValid, isNameValid) ||
                other.isNameValid == isNameValid) &&
            (identical(other.isUrlValid, isUrlValid) ||
                other.isUrlValid == isUrlValid) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, socialInfo, hasDuplicateName,
      isNameValid, isUrlValid, isSubmitting, errorMessage);

  /// Create a copy of TrafficSourcesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TrafficSourcesStateImplCopyWith<_$TrafficSourcesStateImpl> get copyWith =>
      __$$TrafficSourcesStateImplCopyWithImpl<_$TrafficSourcesStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TrafficSourcesStateImplToJson(
      this,
    );
  }
}

abstract class _TrafficSourcesState implements TrafficSourcesState {
  const factory _TrafficSourcesState(
      {final SocialInfo socialInfo,
      final bool hasDuplicateName,
      final bool isNameValid,
      final bool isUrlValid,
      final bool isSubmitting,
      final String? errorMessage}) = _$TrafficSourcesStateImpl;

  factory _TrafficSourcesState.fromJson(Map<String, dynamic> json) =
      _$TrafficSourcesStateImpl.fromJson;

  @override
  SocialInfo get socialInfo;
  @override
  bool get hasDuplicateName;
  @override
  bool get isNameValid;
  @override
  bool get isUrlValid;
  @override
  bool get isSubmitting;
  @override
  String? get errorMessage;

  /// Create a copy of TrafficSourcesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TrafficSourcesStateImplCopyWith<_$TrafficSourcesStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
