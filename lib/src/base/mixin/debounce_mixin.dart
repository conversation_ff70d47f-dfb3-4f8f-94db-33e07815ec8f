import 'package:flutter/material.dart';

/// Mixin that provides debounce functionality to prevent multiple rapid submissions
/// 
/// This mixin can be used by any StatefulWidget that needs to prevent
/// multiple rapid button clicks or form submissions.
mixin DebounceMixin<T extends StatefulWidget> on State<T> {
  bool _isProcessing = false;
  DateTime? _lastClickTime;

  /// Default debounce delay - can be overridden by implementing widgets
  Duration get debounceDelay => const Duration(milliseconds: 500);

  /// Whether to prevent multiple clicks - can be overridden by implementing widgets
  bool get preventMultipleClicks => true;

  /// Check if a click/tap can be processed based on debounce rules
  bool canProcessAction() {
    if (_isProcessing) return false;

    if (!preventMultipleClicks) return true;

    final now = DateTime.now();
    if (_lastClickTime != null && now.difference(_lastClickTime!) < debounceDelay) {
      return false;
    }

    return true;
  }

  /// Execute an action with debounce protection
  /// 
  /// [action] - The function to execute
  /// [onComplete] - Optional callback when action completes
  Future<void> executeWithDebounce(
    VoidCallback action, {
    VoidCallback? onComplete,
  }) async {
    if (!canProcessAction()) return;

    setState(() {
      _isProcessing = true;
      _lastClickTime = DateTime.now();
    });

    try {
      action();
    } finally {
      // Reset processing state after debounce delay
      Future.delayed(debounceDelay, () {
        if (mounted) {
          setState(() {
            _isProcessing = false;
          });
          onComplete?.call();
        }
      });
    }
  }

  /// Get current processing state
  bool get isProcessing => _isProcessing;

  /// Reset the debounce state (useful for testing or manual resets)
  void resetDebounceState() {
    if (mounted) {
      setState(() {
        _isProcessing = false;
        _lastClickTime = null;
      });
    }
  }
}
